# Milestone Analysis Results

## Overview

This directory contains the analysis results generated by the automated milestone analysis system for each milestone specification.

## Directory Structure

```
docs/tech-specs/milestones/analysis-results/
├── README.md                    # This file
├── M0.1/                        # M0.1 milestone analysis results
│   ├── validate-m0.1-final.mjs
│   ├── validate-m0.1-readiness.mjs
│   └── validate-task-breakdown.mjs
├── M0.2/                        # M0.2 milestone analysis results (when analyzed)
└── M1.0/                        # M1.0 milestone analysis results (when analyzed)
```

## Purpose

Each milestone subdirectory contains:

### Analysis Scripts
- **`validate-{milestone}-final.mjs`** - Comprehensive validation without external dependencies
- **`validate-{milestone}-readiness.mjs`** - Detailed readiness assessment with confidence scoring
- **`validate-task-breakdown.mjs`** - Task breakdown quality analysis

### Generated Artifacts (when system runs)
- **`agent-execution-guide-{milestone}.md`** - Agent execution prompts and instructions
- **`{milestone}-analysis-summary.md`** - Comprehensive analysis report
- **`acceptance/{milestone}-acceptance.sh`** - Acceptance test suite

## Relationship to Milestone Specs

Each analysis result directory corresponds to a milestone specification:
- `M0.1/` → `milestone-M0.1.mdx`
- `M0.2/` → `milestone-M0.2.mdx`
- `M1.0/` → `milestone-M1.0.mdx`

## Usage

### Running Analysis Results
```bash
# Run comprehensive validation for M0.1
node docs/tech-specs/milestones/analysis-results/M0.1/validate-m0.1-final.mjs

# Check M0.1 readiness
node docs/tech-specs/milestones/analysis-results/M0.1/validate-m0.1-readiness.mjs

# Validate M0.1 task breakdown
node docs/tech-specs/milestones/analysis-results/M0.1/validate-task-breakdown.mjs
```

### Generating New Analysis Results
```bash
# Analyze any milestone and generate results
./docs/scripts/milestone-analysis/auto-analyze-milestone.sh docs/tech-specs/milestones/milestone-M0.2.mdx

# Results will be generated in docs/scripts/ and can be moved here for tracking
```

## Analysis History

This structure allows tracking of:
- **Analysis Evolution**: How milestone analysis improves over time
- **Validation Results**: Historical validation outcomes
- **Agent Readiness**: Confidence scores and readiness assessments
- **Quality Metrics**: Task breakdown and specification quality trends

## Maintenance

When updating milestone specifications:
1. Re-run automated analysis system
2. Update corresponding analysis results directory
3. Archive previous results if significant changes
4. Update validation scripts to reflect new requirements

## Integration with Analysis System

The automated milestone analysis system (`docs/scripts/milestone-analysis/`) generates these results:
- **Input**: Milestone specification files (`milestone-*.mdx`)
- **Output**: Analysis results stored in this directory structure
- **Purpose**: Track analysis outcomes and enable milestone-specific validation

---

This structure provides a clear audit trail of milestone analysis results and enables tracking of specification quality over time.
