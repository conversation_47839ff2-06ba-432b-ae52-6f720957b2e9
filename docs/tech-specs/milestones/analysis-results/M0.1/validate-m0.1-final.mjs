#!/usr/bin/env node

/**
 * M0.1 Final Validation Script
 * 
 * Comprehensive validation without external dependencies.
 * Provides final confidence assessment for agent execution.
 */

import { readFileSync, existsSync } from 'fs';

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = {
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warn: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`)
};

class M01FinalValidator {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.warnings = 0;
    this.errors = 0;
  }

  check(condition, successMsg, errorMsg, isWarning = false) {
    this.checks++;
    if (condition) {
      log.success(successMsg);
      this.passed++;
      return true;
    } else {
      if (isWarning) {
        log.warn(errorMsg);
        this.warnings++;
      } else {
        log.error(errorMsg);
        this.errors++;
      }
      return false;
    }
  }

  // Simple frontmatter parser (no dependencies)
  parseFrontmatter(content) {
    const frontmatterMatch = content.match(/^---\s*\n([\s\S]*?)\n---/);
    if (!frontmatterMatch) return {};
    
    const frontmatterText = frontmatterMatch[1];
    const frontmatter = {};
    
    frontmatterText.split('\n').forEach(line => {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim();
        const value = line.substring(colonIndex + 1).trim();
        frontmatter[key] = value.replace(/^["']|["']$/g, ''); // Remove quotes
      }
    });
    
    return frontmatter;
  }

  validateSpecificationFile() {
    log.info('Validating M0.1 specification file...');
    
    const specPath = 'docs/tech-specs/milestones/milestone-M0.1.mdx';
    
    this.check(
      existsSync(specPath),
      'M0.1 specification file exists',
      'M0.1 specification file not found'
    );

    if (!existsSync(specPath)) return;

    const content = readFileSync(specPath, 'utf8');
    const frontmatter = this.parseFrontmatter(content);

    // Validate frontmatter
    const requiredFields = ['title', 'description', 'created', 'version', 'status', 'tags'];
    requiredFields.forEach(field => {
      this.check(
        field in frontmatter,
        `Frontmatter field '${field}' present`,
        `Missing frontmatter field: ${field}`
      );
    });

    // Validate content sections
    const requiredSections = [
      '## 🧳 Toolchain Versions',
      '## 🎯 Definition of Done',
      '## 📦 Deliverables',
      '## 🗂 Directory Layout',
      '## 🧠 Key Decisions',
      '## ✅ Success Criteria',
      '## 🔨 Task Breakdown',
      '## 🤖 CI Pipeline',
      '## 🧪 Acceptance Tests'
    ];

    requiredSections.forEach(section => {
      this.check(
        content.includes(section),
        `Section present: ${section}`,
        `Missing required section: ${section}`
      );
    });

    // Validate specific content
    this.check(
      content.includes('pnpm run build-kg'),
      'Build command specified',
      'Missing build command specification'
    );

    this.check(
      content.includes('--dry-run'),
      'Dry-run capability specified',
      'Missing dry-run capability'
    );

    // Count success criteria
    const successCriteriaMatches = content.match(/- \[ \] \*\*SC-\d+\*\*/g);
    this.check(
      successCriteriaMatches && successCriteriaMatches.length >= 5,
      `Found ${successCriteriaMatches?.length || 0} success criteria`,
      'Insufficient success criteria (need at least 5)'
    );

    // Count tasks
    const taskMatches = content.match(/\| \d+ \|.*\|.*\|.*\|/g);
    this.check(
      taskMatches && taskMatches.length >= 8,
      `Found ${taskMatches?.length || 0} tasks in breakdown`,
      'Insufficient task breakdown (need at least 8 tasks)'
    );
  }

  validateProjectStructure() {
    log.info('Validating project structure...');

    // Check workspace configuration
    this.check(
      existsSync('code/pnpm-workspace.yaml'),
      'pnpm workspace configuration exists',
      'Missing pnpm workspace configuration'
    );

    // Check required directories
    const requiredDirs = [
      'code/apps',
      'code/apps/api',
      'code/apps/web',
      'code/packages',
      'docs/tech-specs',
      'docs/scripts'
    ];

    requiredDirs.forEach(dir => {
      this.check(
        existsSync(dir),
        `Directory exists: ${dir}`,
        `Missing directory: ${dir}`
      );
    });

    // Check package.json files
    this.check(
      existsSync('code/package.json'),
      'Main package.json exists',
      'Missing main package.json'
    );

    this.check(
      existsSync('package.json'),
      'Root package.json exists',
      'Missing root package.json'
    );
  }

  validateToolingReadiness() {
    log.info('Validating tooling readiness...');

    // Check validation scripts
    const scripts = [
      'docs/scripts/spec-lint.mjs',
      'docs/scripts/acceptance/m0.1-acceptance.sh',
      'docs/scripts/validate-m0.1-readiness.mjs',
      'docs/scripts/validate-task-breakdown.mjs'
    ];

    scripts.forEach(script => {
      this.check(
        existsSync(script),
        `Script exists: ${script}`,
        `Missing script: ${script}`
      );
    });

    // Check if acceptance script is executable
    this.check(
      existsSync('docs/scripts/acceptance/m0.1-acceptance.sh'),
      'M0.1 acceptance script exists',
      'Missing M0.1 acceptance script'
    );
  }

  validateImplementationReadiness() {
    log.info('Validating implementation readiness...');

    // These should NOT exist yet (good for fresh implementation)
    this.check(
      !existsSync('code/packages/spec-parser-lib'),
      'spec-parser-lib not yet implemented (ready for creation)',
      'spec-parser-lib already exists',
      true
    );

    this.check(
      !existsSync('code/packages/kg-cli'),
      'kg-cli not yet implemented (ready for creation)',
      'kg-cli already exists',
      true
    );

    this.check(
      !existsSync('kg.jsonld') && !existsSync('kg.yaml'),
      'Output files not present (clean slate)',
      'Output files already exist',
      true
    );

    this.check(
      !existsSync('.github/workflows/graph.yml'),
      'CI workflow not yet implemented (ready for creation)',
      'CI workflow already exists',
      true
    );
  }

  validateAgentExecutability() {
    log.info('Validating agent executability...');

    const specPath = 'docs/tech-specs/milestones/milestone-M0.1.mdx';
    if (!existsSync(specPath)) return;

    const content = readFileSync(specPath, 'utf8');

    // Check for specific implementation details
    const executabilityChecks = [
      { pattern: 'code/packages/spec-parser-lib/', desc: 'Specific package paths' },
      { pattern: 'parse-specs.ts', desc: 'Core implementation files' },
      { pattern: 'build-kg.ts', desc: 'CLI implementation files' },
      { pattern: 'kg-schema.yml', desc: 'Schema file specification' },
      { pattern: '.github/workflows/graph.yml', desc: 'CI workflow path' },
      { pattern: '.vscode/extensions.json', desc: 'Editor configuration' },
      { pattern: 'docs/README.md', desc: 'Documentation files' }
    ];

    executabilityChecks.forEach(check => {
      this.check(
        content.includes(check.pattern),
        `${check.desc} specified`,
        `Missing ${check.desc} specification`
      );
    });

    // Check for command specifications
    const commands = [
      'pnpm run build-kg',
      'node scripts/spec-lint.mjs',
      'pnpm install',
      'pnpm run build-kg -- --dry-run'
    ];

    commands.forEach(cmd => {
      this.check(
        content.includes(cmd),
        `Command specified: ${cmd}`,
        `Missing command: ${cmd}`
      );
    });
  }

  generateFinalReport() {
    console.log('\n' + '='.repeat(70));
    console.log('🎯 M0.1 FINAL VALIDATION REPORT');
    console.log('='.repeat(70));

    const successRate = Math.round((this.passed / this.checks) * 100);
    
    log.info(`Total checks performed: ${this.checks}`);
    log.success(`Checks passed: ${this.passed}`);
    
    if (this.warnings > 0) {
      log.warn(`Warnings: ${this.warnings}`);
    }
    
    if (this.errors > 0) {
      log.error(`Errors: ${this.errors}`);
    }

    console.log(`\n📊 Overall Success Rate: ${successRate}%`);

    // Determine confidence level
    let confidence, recommendation, status;
    
    if (successRate >= 95 && this.errors === 0) {
      confidence = '95%+';
      recommendation = '✅ PROCEED WITH AGENT EXECUTION';
      status = 'READY';
      log.success('🎉 MILESTONE M0.1 IS READY FOR AGENT EXECUTION!');
    } else if (successRate >= 90 && this.errors <= 2) {
      confidence = '90-94%';
      recommendation = '⚠️  PROCEED WITH CAUTION';
      status = 'MOSTLY READY';
      log.warn('⚠️  Milestone M0.1 is mostly ready with minor issues');
    } else {
      confidence = '<90%';
      recommendation = '❌ RESOLVE ISSUES BEFORE EXECUTION';
      status = 'NOT READY';
      log.error('❌ Milestone M0.1 has significant issues');
    }

    console.log(`\n🎯 Confidence Level: ${confidence}`);
    console.log(`📋 Status: ${status}`);
    console.log(`🚀 Recommendation: ${recommendation}`);

    if (this.errors === 0 && this.warnings === 0) {
      console.log('\n✨ Perfect score! All validations passed.');
      console.log('🤖 Agent can execute with maximum confidence.');
    } else if (this.errors === 0) {
      console.log('\n✅ No critical errors found.');
      console.log('⚠️  Minor warnings are acceptable for execution.');
    }

    console.log('\n📚 Next Steps:');
    if (successRate >= 95) {
      console.log('   1. Begin agent execution following task sequence');
      console.log('   2. Use validation scripts during implementation');
      console.log('   3. Run acceptance tests after completion');
    } else {
      console.log('   1. Review and resolve critical errors');
      console.log('   2. Re-run validation after fixes');
      console.log('   3. Proceed when confidence ≥95%');
    }

    return successRate >= 95 && this.errors === 0;
  }

  run() {
    console.log('🔍 Starting M0.1 Final Validation...\n');

    this.validateSpecificationFile();
    this.validateProjectStructure();
    this.validateToolingReadiness();
    this.validateImplementationReadiness();
    this.validateAgentExecutability();

    const isReady = this.generateFinalReport();
    process.exit(isReady ? 0 : 1);
  }
}

// Run the validator
const validator = new M01FinalValidator();
validator.run();
