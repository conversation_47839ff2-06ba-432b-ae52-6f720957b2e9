#!/usr/bin/env node

/**
 * Task Breakdown Validation Script
 *
 * Validates that the task breakdown in M0.1 is complete and executable
 * by software agents with clear, unambiguous instructions.
 */

import { readFileSync, existsSync } from "fs";

// Import gray-matter from the code directory where it's installed
let matter;
try {
  const { createRequire } = await import("module");
  const require = createRequire(import.meta.url);
  matter = require("../../code/node_modules/gray-matter");
} catch (error) {
  console.error(
    "❌ gray-matter dependency not found. Please run: cd code && pnpm install"
  );
  process.exit(1);
}

const colors = {
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m"
};

const log = {
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warn: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`)
};

class TaskBreakdownValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.tasks = [];
  }

  parseTaskBreakdown(content) {
    // Extract task breakdown table
    const taskSectionMatch = content.match(
      /## 🔨 Task Breakdown\s*\n([\s\S]*?)(?=\n##|$)/
    );
    if (!taskSectionMatch) {
      this.errors.push("Task breakdown section not found");
      return;
    }

    const taskSection = taskSectionMatch[1];

    // Parse table rows (skip header)
    const tableRows = taskSection
      .split("\n")
      .filter((line) => line.trim().startsWith("|") && !line.includes("---"))
      .slice(1); // Skip header row

    tableRows.forEach((row, index) => {
      const columns = row
        .split("|")
        .map((col) => col.trim())
        .filter((col) => col);

      if (columns.length >= 4) {
        const task = {
          number: columns[0],
          branch: columns[1].replace(/`/g, ""), // Remove backticks from branch names
          description: columns[2],
          owner: columns[3],
          rowIndex: index + 1
        };
        this.tasks.push(task);
      }
    });

    log.info(`Found ${this.tasks.length} tasks in breakdown`);
  }

  validateTaskCompleteness() {
    log.info("Validating task completeness...");

    // Check minimum number of tasks
    if (this.tasks.length < 8) {
      this.errors.push(
        `Insufficient tasks: found ${this.tasks.length}, expected at least 8`
      );
    } else {
      log.success(`Task count adequate: ${this.tasks.length} tasks`);
    }

    // Validate each task
    this.tasks.forEach((task) => {
      this.validateTask(task);
    });
  }

  validateTask(task) {
    const taskId = `Task ${task.number}`;

    // Check task number format
    if (!/^\d{2}$/.test(task.number)) {
      this.errors.push(
        `${taskId}: Invalid number format '${task.number}' (should be 01, 02, etc.)`
      );
    }

    // Check branch naming convention
    if (!task.branch.startsWith("m0.1/")) {
      this.errors.push(
        `${taskId}: Branch should start with 'm0.1/' (got '${task.branch}')`
      );
    }

    // Check description specificity
    if (task.description.length < 20) {
      this.warnings.push(
        `${taskId}: Description may be too brief: '${task.description}'`
      );
    }

    // Check for action verbs
    const actionVerbs = [
      "scaffold",
      "implement",
      "create",
      "add",
      "commit",
      "run",
      "merge"
    ];
    const hasActionVerb = actionVerbs.some((verb) =>
      task.description.toLowerCase().includes(verb)
    );

    if (!hasActionVerb) {
      this.warnings.push(
        `${taskId}: Description should start with action verb`
      );
    }

    // Check owner assignment
    const validOwners = ["BE", "FE", "PM", "DevOps", "Lead"];
    if (!validOwners.includes(task.owner)) {
      this.warnings.push(
        `${taskId}: Unusual owner '${task.owner}' (expected: ${validOwners.join(
          ", "
        )})`
      );
    }

    // Validate specific task requirements
    this.validateSpecificTaskRequirements(task);
  }

  validateSpecificTaskRequirements(task) {
    const taskId = `Task ${task.number}`;
    const desc = task.description.toLowerCase();

    // Task-specific validations based on M0.1 requirements
    if (desc.includes("spec-parser-lib")) {
      if (!desc.includes("typescript")) {
        this.warnings.push(
          `${taskId}: Should mention TypeScript setup for spec-parser-lib`
        );
      }
    }

    if (desc.includes("frontmatter")) {
      if (!desc.includes("gray-matter") && !desc.includes("extraction")) {
        this.warnings.push(
          `${taskId}: Should specify gray-matter or extraction method`
        );
      }
    }

    if (desc.includes("kg-cli")) {
      if (!desc.includes("cli") && !desc.includes("command")) {
        this.warnings.push(`${taskId}: Should clarify CLI implementation`);
      }
    }

    if (desc.includes("ci") || desc.includes("workflow")) {
      if (!desc.includes("github") && !desc.includes(".github")) {
        this.warnings.push(`${taskId}: Should specify GitHub Actions workflow`);
      }
    }

    if (desc.includes("test")) {
      if (!desc.includes("jest") && !desc.includes("acceptance")) {
        this.warnings.push(`${taskId}: Should specify test framework or type`);
      }
    }
  }

  validateTaskSequencing() {
    log.info("Validating task sequencing...");

    // Check for logical dependencies
    const taskDescriptions = this.tasks.map((t) => t.description.toLowerCase());

    // Parser lib should come before CLI
    const parserIndex = taskDescriptions.findIndex((desc) =>
      desc.includes("spec-parser-lib")
    );
    const cliIndex = taskDescriptions.findIndex((desc) =>
      desc.includes("kg-cli")
    );

    if (parserIndex > cliIndex && parserIndex !== -1 && cliIndex !== -1) {
      this.warnings.push("spec-parser-lib should be implemented before kg-cli");
    }

    // Tests should come after implementation
    const testIndex = taskDescriptions.findIndex((desc) =>
      desc.includes("test")
    );
    const implIndex = Math.max(parserIndex, cliIndex);

    if (testIndex < implIndex && testIndex !== -1 && implIndex !== -1) {
      this.warnings.push("Tests should come after implementation tasks");
    }

    // CI should come after core implementation
    const ciIndex = taskDescriptions.findIndex(
      (desc) => desc.includes("ci") || desc.includes("workflow")
    );
    if (ciIndex < implIndex && ciIndex !== -1 && implIndex !== -1) {
      this.warnings.push("CI setup should come after core implementation");
    }

    log.success("Task sequencing validation complete");
  }

  validateBranchNaming() {
    log.info("Validating branch naming consistency...");

    const branchNames = this.tasks.map((t) => t.branch);
    const uniqueBranches = new Set(branchNames);

    if (branchNames.length !== uniqueBranches.size) {
      this.errors.push("Duplicate branch names found");
    }

    // Check for consistent naming pattern
    const invalidBranches = branchNames.filter(
      (branch) => !branch.match(/^m0\.1\/[a-z-]+$/)
    );

    if (invalidBranches.length > 0) {
      this.warnings.push(
        `Inconsistent branch naming: ${invalidBranches.join(", ")}`
      );
    }

    log.success("Branch naming validation complete");
  }

  validateDeliverablesCoverage(content) {
    log.info("Validating deliverables coverage...");

    // Extract deliverables from the spec
    const deliverablesMatch = content.match(
      /## 📦 Deliverables\s*\n([\s\S]*?)(?=\n##|$)/
    );
    if (!deliverablesMatch) {
      this.errors.push("Deliverables section not found");
      return;
    }

    const deliverables = deliverablesMatch[1];
    const taskDescriptions = this.tasks.map((t) => t.description).join(" ");

    // Check key deliverables are covered in tasks
    const keyDeliverables = [
      "spec-parser-lib",
      "kg-cli",
      "kg-schema.yml",
      "graph.yml",
      "extensions.json",
      "README.md"
    ];

    keyDeliverables.forEach((deliverable) => {
      if (!taskDescriptions.toLowerCase().includes(deliverable.toLowerCase())) {
        this.warnings.push(
          `Deliverable '${deliverable}' not explicitly covered in tasks`
        );
      }
    });

    log.success("Deliverables coverage validation complete");
  }

  generateReport() {
    console.log("\n" + "=".repeat(60));
    console.log("📋 TASK BREAKDOWN VALIDATION REPORT");
    console.log("=".repeat(60));

    log.info(`Total tasks analyzed: ${this.tasks.length}`);

    if (this.errors.length === 0 && this.warnings.length === 0) {
      log.success("🎉 Task breakdown is well-defined and agent-executable!");
      console.log("\n✅ All tasks have clear, actionable descriptions");
      console.log("✅ Task sequencing is logical");
      console.log("✅ Branch naming is consistent");
      console.log("✅ All deliverables are covered");
      return true;
    }

    if (this.errors.length > 0) {
      console.log("\n🔴 CRITICAL ISSUES:");
      this.errors.forEach((error) => console.log(`   • ${error}`));
    }

    if (this.warnings.length > 0) {
      console.log("\n🟡 WARNINGS:");
      this.warnings.forEach((warning) => console.log(`   • ${warning}`));
    }

    const severity = this.errors.length > 0 ? "HIGH" : "MEDIUM";
    console.log(`\n📊 Issue Severity: ${severity}`);

    if (this.errors.length === 0) {
      log.warn("Task breakdown has warnings but is likely executable");
      return true;
    } else {
      log.error("Task breakdown has critical issues that must be resolved");
      return false;
    }
  }

  run() {
    console.log("🔍 Starting Task Breakdown Validation...\n");

    const specPath = "docs/tech-specs/milestones/milestone-M0.1.mdx";

    if (!existsSync(specPath)) {
      log.error("M0.1 specification file not found");
      process.exit(1);
    }

    const content = readFileSync(specPath, "utf8");
    const { content: body } = matter(content);

    this.parseTaskBreakdown(body);
    this.validateTaskCompleteness();
    this.validateTaskSequencing();
    this.validateBranchNaming();
    this.validateDeliverablesCoverage(body);

    const isValid = this.generateReport();
    process.exit(isValid ? 0 : 1);
  }
}

// Run the validator
const validator = new TaskBreakdownValidator();
validator.run();
