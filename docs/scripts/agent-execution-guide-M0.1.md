# M0.1 Agent Execution Guide

## 🎯 Execution Overview
**Milestone**: Milestone M0.1 — Knowledge-Graph Bootstrap
**Confidence**: 95%
**Status**: READY FOR EXECUTION

## 🚀 Agent Execution Prompts

### 80% Standard Execution Pattern
Use these prompts for any milestone execution:

#### 1. Pre-Execution Setup
```bash
# Validate milestone readiness
node docs/scripts/validate-M0.1.mjs

# Ensure dependencies are installed
cd code && pnpm install && cd ..

# Create feature branch
git checkout -b implement-M0.1
```

#### 2. Standard Project Structure Validation
**Agent Prompt**: "Verify the following directories exist and create them if missing:"
- `code/packages/` - For reusable packages and libraries
- `code/apps/` - For applications (api, web, docs-site)
- `docs/tech-specs/` - For technical specifications
- `.github/workflows/` - For CI/CD workflows

#### 3. Package Configuration Setup
**Agent Prompt**: "Ensure the following scripts are available in package.json:"
- `build` - Build all packages and applications
- `test` - Run test suites
- `lint` - Run linting and code quality checks
- `build-kg` - Build knowledge graph from specifications

#### 4. Dependency Management
**Agent Prompt**: "Install the following dependencies if not present:"
**Core Dependencies:**
- `typescript` - TypeScript compiler and types
- `gray-matter` - Front-matter parsing for MDX files
- `yaml` - YAML parsing and serialization
- `uuid` - UUID generation for unique identifiers

### 20% Milestone-Specific Execution

#### Knowledge Graph Specific Tasks

**Agent Prompt**: "You are implementing a knowledge graph system. Follow these specific steps:"

1. **Create Spec Parser Library**
   ```bash
   mkdir -p code/packages/spec-parser-lib/src
   cd code/packages/spec-parser-lib
   # Initialize package.json with dependencies: gray-matter, yaml, uuid
   ```

2. **Implement MDX Parsing**
   - Create `src/parse-specs.ts` that can extract frontmatter from MDX files
   - Use gray-matter library for frontmatter parsing
   - Extract headings and content structure

3. **Create Knowledge Graph CLI**
   ```bash
   mkdir -p code/packages/kg-cli/src
   cd code/packages/kg-cli
   # Create CLI tool that reads specs and outputs graph files
   ```

4. **Implement Graph Generation**
   - Create `src/build-kg.ts` that scans `docs/tech-specs/**/*.mdx`
   - Generate both `kg.jsonld` and `kg.yaml` files
   - Support `--dry-run` mode for validation

5. **Create Schema Definition**
   - Create `kg-schema.yml` in repository root
   - Define entities: milestone, component, relationship types
   - Include confidence scoring and metadata

## 📋 Task Execution Sequence

**Agent Prompt**: "Execute the following tasks in sequence. Each task should be completed on its own branch:"

## ✅ Success Criteria Validation

**Agent Prompt**: "Validate each success criterion before marking milestone complete:"


**Validation Commands:**
`node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx`
`- [ ] **SC-6** Agent dry-run passes:`
`pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx`

## 🧪 Continuous Validation
Run these commands throughout implementation:

```bash
# After each major step
node docs/scripts/validate-M0.1.mjs

# Before committing
bash docs/scripts/acceptance/M0.1-acceptance.sh

# Final validation
pnpm run build && pnpm run test && pnpm run lint
```

## 🔄 Rollback Strategy
If execution fails:
1. **Identify failure point**: Check validation output
2. **Isolate issue**: Run specific validation checks
3. **Fix and retry**: Address issue and re-validate
4. **Reset if needed**: `git checkout main && git branch -D implement-M0.1`

## 🤖 Agent-Specific Instructions

### For LLM Agents
- Use the prompts above as direct instructions
- Validate each step before proceeding
- Ask for clarification if any step is unclear

### For Autonomous Agents
- Execute validation scripts before each major step
- Use exit codes to determine success/failure
- Implement retry logic for transient failures

### For Human-Agent Collaboration
- Human reviews milestone analysis
- Agent executes standard 80% pattern
- Human handles complex 20% decisions

---
*Generated by auto-analyze-milestone.sh on Thu May 29 12:05:38 IST 2025*
*Confidence Score: 95% | Checks: 20/21*
