#!/usr/bin/env node

/**
 * M0.1 Readiness Validation Script
 *
 * Validates that the milestone M0.1 specification is complete and ready
 * for agent execution with 95% confidence.
 */

import { readFileSync, existsSync } from "fs";
import { resolve, join } from "path";

// Import gray-matter from the code directory where it's installed
let matter;
try {
  const { createRequire } = await import("module");
  const require = createRequire(import.meta.url);
  matter = require("../../code/node_modules/gray-matter");
} catch (error) {
  console.error(
    "❌ gray-matter dependency not found. Please run: cd code && pnpm install"
  );
  process.exit(1);
}

// Colors for console output
const colors = {
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m"
};

const log = {
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warn: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`)
};

class M01ReadinessValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.checks = 0;
    this.passed = 0;
  }

  check(condition, successMsg, errorMsg, isWarning = false) {
    this.checks++;
    if (condition) {
      log.success(successMsg);
      this.passed++;
      return true;
    } else {
      if (isWarning) {
        log.warn(errorMsg);
        this.warnings.push(errorMsg);
      } else {
        log.error(errorMsg);
        this.errors.push(errorMsg);
      }
      return false;
    }
  }

  validateSpecFile() {
    log.info("Validating M0.1 specification file...");

    const specPath = "docs/tech-specs/milestones/milestone-M0.1.mdx";

    this.check(
      existsSync(specPath),
      "M0.1 specification file exists",
      "M0.1 specification file not found"
    );

    if (!existsSync(specPath)) return;

    const content = readFileSync(specPath, "utf8");
    const { data: frontmatter, content: body } = matter(content);

    // Validate frontmatter
    const requiredFields = [
      "title",
      "description",
      "created",
      "version",
      "status",
      "tags"
    ];
    requiredFields.forEach((field) => {
      this.check(
        field in frontmatter,
        `Frontmatter field '${field}' present`,
        `Missing frontmatter field: ${field}`
      );
    });

    // Validate specific content requirements
    this.check(
      frontmatter.title?.includes("M0.1"),
      "Title correctly references M0.1",
      "Title should reference M0.1"
    );

    this.check(
      frontmatter.status === "Draft" || frontmatter.status === "Approved",
      `Status is appropriate: ${frontmatter.status}`,
      "Status should be Draft or Approved for implementation"
    );

    // Validate required sections
    const requiredSections = [
      "## 🧳 Toolchain Versions",
      "## 🎯 Definition of Done",
      "## 📦 Deliverables",
      "## 🗂 Directory Layout",
      "## 🧠 Key Decisions",
      "## ✅ Success Criteria",
      "## 🔨 Task Breakdown",
      "## 🤖 CI Pipeline",
      "## 🧪 Acceptance Tests"
    ];

    requiredSections.forEach((section) => {
      this.check(
        body.includes(section),
        `Section present: ${section}`,
        `Missing required section: ${section}`
      );
    });

    // Validate toolchain versions are specified
    this.check(
      body.includes("node:") &&
        body.includes("pnpm:") &&
        body.includes("typescript:"),
      "Toolchain versions specified",
      "Missing toolchain version specifications"
    );

    // Validate success criteria are testable
    const successCriteriaMatch = body.match(/- \[ \] \*\*SC-\d+\*\*/g);
    this.check(
      successCriteriaMatch && successCriteriaMatch.length >= 5,
      `Found ${successCriteriaMatch?.length || 0} success criteria`,
      "Insufficient success criteria (need at least 5)"
    );

    // Validate task breakdown
    const taskBreakdownMatch = body.match(/\| \d+ \|.*\|.*\|.*\|/g);
    this.check(
      taskBreakdownMatch && taskBreakdownMatch.length >= 8,
      `Found ${taskBreakdownMatch?.length || 0} tasks in breakdown`,
      "Insufficient task breakdown (need at least 8 tasks)"
    );
  }

  validateProjectStructure() {
    log.info("Validating project structure readiness...");

    // Check workspace configuration
    this.check(
      existsSync("code/pnpm-workspace.yaml"),
      "pnpm workspace configuration exists",
      "Missing pnpm workspace configuration"
    );

    if (existsSync("code/pnpm-workspace.yaml")) {
      const workspaceContent = readFileSync("code/pnpm-workspace.yaml", "utf8");
      this.check(
        workspaceContent.includes("packages/*"),
        "Workspace includes packages/* glob",
        "Workspace missing packages/* glob"
      );
    }

    // Check required directories
    const requiredDirs = [
      "code/apps",
      "code/apps/api",
      "code/apps/web",
      "code/packages",
      "docs/tech-specs"
    ];

    requiredDirs.forEach((dir) => {
      this.check(
        existsSync(dir),
        `Directory exists: ${dir}`,
        `Missing directory: ${dir}`
      );
    });

    // Check package.json structure
    this.check(
      existsSync("code/package.json"),
      "Main package.json exists",
      "Missing main package.json"
    );

    if (existsSync("code/package.json")) {
      const pkg = JSON.parse(readFileSync("code/package.json", "utf8"));

      this.check(
        pkg.devDependencies?.["gray-matter"],
        "gray-matter dependency available",
        "Missing gray-matter dependency"
      );

      this.check(
        pkg.devDependencies?.["typescript"],
        "TypeScript dependency available",
        "Missing TypeScript dependency"
      );
    }
  }

  validateToolingReadiness() {
    log.info("Validating tooling readiness...");

    // Check if spec-lint script exists and works
    this.check(
      existsSync("docs/scripts/spec-lint.mjs"),
      "spec-lint script exists",
      "Missing spec-lint script"
    );

    // Check if acceptance test script exists
    this.check(
      existsSync("docs/scripts/acceptance/m0.1-acceptance.sh"),
      "M0.1 acceptance test script exists",
      "Missing M0.1 acceptance test script"
    );

    // Check for VS Code configuration readiness
    this.check(
      existsSync(".vscode") || true, // This is optional
      "VS Code directory exists or will be created",
      "VS Code directory missing",
      true
    );
  }

  validateImplementationReadiness() {
    log.info("Validating implementation readiness...");

    // Check if target packages already exist (should not for fresh implementation)
    this.check(
      !existsSync("code/packages/spec-parser-lib"),
      "spec-parser-lib package not yet implemented (good)",
      "spec-parser-lib package already exists",
      true
    );

    this.check(
      !existsSync("code/packages/kg-cli"),
      "kg-cli package not yet implemented (good)",
      "kg-cli package already exists",
      true
    );

    // Check if output files don't exist yet
    this.check(
      !existsSync("kg.jsonld") && !existsSync("kg.yaml"),
      "Output files not present (good for fresh start)",
      "Output files already exist",
      true
    );

    // Check if CI workflow doesn't exist yet
    this.check(
      !existsSync(".github/workflows/graph.yml"),
      "CI workflow not yet implemented (good)",
      "CI workflow already exists",
      true
    );
  }

  validateAgentExecutability() {
    log.info("Validating agent executability...");

    // Check if all required information is present for agent execution
    const specPath = "docs/tech-specs/milestones/milestone-M0.1.mdx";
    if (!existsSync(specPath)) return;

    const content = readFileSync(specPath, "utf8");

    // Check for specific file paths
    this.check(
      content.includes("code/packages/spec-parser-lib/"),
      "Specific package paths defined",
      "Missing specific package paths"
    );

    // Check for exact commands
    this.check(
      content.includes("pnpm run build-kg"),
      "Build command specified",
      "Missing build command specification"
    );

    // Check for test commands
    this.check(
      content.includes("node scripts/spec-lint.mjs"),
      "Lint command specified",
      "Missing lint command specification"
    );

    // Check for CI configuration details
    this.check(
      content.includes(".github/workflows/graph.yml"),
      "CI workflow path specified",
      "Missing CI workflow path"
    );

    // Check for schema file specification
    this.check(
      content.includes("kg-schema.yml"),
      "Schema file specified",
      "Missing schema file specification"
    );
  }

  generateReport() {
    console.log("\n" + "=".repeat(60));
    console.log("📊 M0.1 READINESS VALIDATION REPORT");
    console.log("=".repeat(60));

    const successRate = Math.round((this.passed / this.checks) * 100);

    log.info(`Total checks: ${this.checks}`);
    log.success(`Passed: ${this.passed}`);

    if (this.warnings.length > 0) {
      log.warn(`Warnings: ${this.warnings.length}`);
    }

    if (this.errors.length > 0) {
      log.error(`Errors: ${this.errors.length}`);
    }

    console.log(`\n📈 Success Rate: ${successRate}%`);

    if (successRate >= 95) {
      log.success("🎉 MILESTONE M0.1 IS READY FOR AGENT EXECUTION!");
      console.log(
        "\n✅ Confidence Level: 95%+ - Agent can proceed with implementation"
      );
    } else if (successRate >= 85) {
      log.warn("⚠️  Milestone M0.1 is mostly ready but has some issues");
      console.log(
        "\n🔧 Confidence Level: 85-94% - Review and fix issues before agent execution"
      );
    } else {
      log.error("❌ Milestone M0.1 is not ready for agent execution");
      console.log(
        "\n🚫 Confidence Level: <85% - Significant issues must be resolved"
      );
    }

    if (this.errors.length > 0) {
      console.log("\n🔴 CRITICAL ISSUES TO RESOLVE:");
      this.errors.forEach((error) => console.log(`   • ${error}`));
    }

    if (this.warnings.length > 0) {
      console.log("\n🟡 WARNINGS (may be acceptable):");
      this.warnings.forEach((warning) => console.log(`   • ${warning}`));
    }

    return successRate >= 95;
  }

  run() {
    console.log("🔍 Starting M0.1 Readiness Validation...\n");

    this.validateSpecFile();
    this.validateProjectStructure();
    this.validateToolingReadiness();
    this.validateImplementationReadiness();
    this.validateAgentExecutability();

    const isReady = this.generateReport();
    process.exit(isReady ? 0 : 1);
  }
}

// Run the validator
const validator = new M01ReadinessValidator();
validator.run();
