#!/usr/bin/env bash
set -euo pipefail

# Automated Milestone Analysis Script
# Performs comprehensive milestone analysis for agent execution readiness

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OUTPUT_DIR="$SCRIPT_DIR"
CONFIDENCE_THRESHOLD=95
VERBOSE=false
SCRIPTS_ONLY=false
CONFIG_FILE=""

# Helper functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warn() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

show_usage() {
    cat << EOF
Usage: $0 <milestone-file> [options]

Automated milestone analysis for agent execution readiness.

Arguments:
    milestone-file    Path to milestone MDX file

Options:
    --config FILE     Use custom analysis configuration
    --threshold NUM   Set confidence threshold (default: 95)
    --scripts-only    Generate only validation scripts
    --verbose         Enable verbose output
    --output-dir DIR  Set output directory (default: docs/scripts)
    --help           Show this help message

Examples:
    $0 docs/tech-specs/milestones/milestone-M0.2.mdx
    $0 milestone-M1.mdx --threshold 90 --verbose
    $0 milestone-M2.mdx --scripts-only --output-dir ./validation

EOF
}

parse_arguments() {
    if [[ $# -eq 0 ]]; then
        show_usage
        exit 1
    fi

    MILESTONE_FILE="$1"
    shift

    while [[ $# -gt 0 ]]; do
        case $1 in
            --config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --threshold)
                CONFIDENCE_THRESHOLD="$2"
                shift 2
                ;;
            --scripts-only)
                SCRIPTS_ONLY=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Validate milestone file exists
    if [[ ! -f "$MILESTONE_FILE" ]]; then
        log_error "Milestone file not found: $MILESTONE_FILE"
        exit 1
    fi

    # Extract milestone identifier from filename
    MILESTONE_ID=$(basename "$MILESTONE_FILE" .mdx | sed 's/milestone-//')
}

extract_milestone_info() {
    log_info "Extracting milestone information..."

    # Extract title and basic info from frontmatter
    MILESTONE_TITLE=$(grep -A 10 "^---" "$MILESTONE_FILE" | grep "title:" | cut -d':' -f2- | xargs)
    MILESTONE_VERSION=$(grep -A 10 "^---" "$MILESTONE_FILE" | grep "version:" | cut -d':' -f2- | xargs)
    MILESTONE_STATUS=$(grep -A 10 "^---" "$MILESTONE_FILE" | grep "status:" | cut -d':' -f2- | xargs)

    if [[ $VERBOSE == true ]]; then
        log_info "Title: $MILESTONE_TITLE"
        log_info "Version: $MILESTONE_VERSION"
        log_info "Status: $MILESTONE_STATUS"
    fi
}

run_core_analysis() {
    log_info "Running core milestone analysis..."

    # Generate the core analyzer if it doesn't exist
    if [[ ! -f "$SCRIPT_DIR/analyze-milestone-core.mjs" ]]; then
        generate_core_analyzer
    fi

    # Run analysis and capture results
    if command -v jq >/dev/null 2>&1; then
        ANALYSIS_RESULT=$(node "$SCRIPT_DIR/analyze-milestone-core.mjs" "$MILESTONE_FILE" --json)
        CONFIDENCE_SCORE=$(echo "$ANALYSIS_RESULT" | jq -r '.confidence_score')
        TOTAL_CHECKS=$(echo "$ANALYSIS_RESULT" | jq -r '.total_checks')
        PASSED_CHECKS=$(echo "$ANALYSIS_RESULT" | jq -r '.passed_checks')
        ERRORS=$(echo "$ANALYSIS_RESULT" | jq -r '.errors | length')
        WARNINGS=$(echo "$ANALYSIS_RESULT" | jq -r '.warnings | length')
    else
        # Fallback without jq
        node "$SCRIPT_DIR/analyze-milestone-core.mjs" "$MILESTONE_FILE" > /tmp/analysis_output.txt
        CONFIDENCE_SCORE=$(grep "Confidence:" /tmp/analysis_output.txt | cut -d' ' -f2 | tr -d '%')
        TOTAL_CHECKS=$(grep "Checks:" /tmp/analysis_output.txt | cut -d'/' -f2)
        PASSED_CHECKS=$(grep "Checks:" /tmp/analysis_output.txt | cut -d':' -f2 | cut -d'/' -f1 | xargs)
        ERRORS=0
        WARNINGS=0
    fi

    log_success "Analysis complete: $CONFIDENCE_SCORE% confidence ($PASSED_CHECKS/$TOTAL_CHECKS checks passed)"

    if [[ $ERRORS -gt 0 ]]; then
        log_warn "$ERRORS error(s) found"
    fi

    if [[ $WARNINGS -gt 0 ]]; then
        log_warn "$WARNINGS warning(s) found"
    fi
}

generate_validation_scripts() {
    log_info "Generating validation scripts..."

    # Generate milestone-specific validation script
    VALIDATION_SCRIPT="$OUTPUT_DIR/validate-${MILESTONE_ID}.mjs"

    cat > "$VALIDATION_SCRIPT" << 'EOF'
#!/usr/bin/env node

/**
 * Auto-generated validation script for MILESTONE_PLACEHOLDER
 * Generated by auto-analyze-milestone.sh
 */

import { readFileSync, existsSync } from 'fs';

// This is a template - customize based on milestone requirements
class MilestoneValidator {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.errors = [];
    this.warnings = [];
  }

  // Add milestone-specific validation methods here

  run() {
    console.log('🔍 Validating MILESTONE_PLACEHOLDER...');
    // Implementation will be generated based on milestone analysis
    process.exit(0);
  }
}

const validator = new MilestoneValidator();
validator.run();
EOF

    # Replace placeholder with actual milestone ID
    sed -i.bak "s/MILESTONE_PLACEHOLDER/$MILESTONE_ID/g" "$VALIDATION_SCRIPT" && rm "$VALIDATION_SCRIPT.bak"
    chmod +x "$VALIDATION_SCRIPT"

    log_success "Generated: $VALIDATION_SCRIPT"
}

generate_acceptance_tests() {
    log_info "Generating acceptance test suite..."

    # Ensure acceptance directory exists
    mkdir -p "$OUTPUT_DIR/acceptance"

    ACCEPTANCE_SCRIPT="$OUTPUT_DIR/acceptance/${MILESTONE_ID}-acceptance.sh"

    cat > "$ACCEPTANCE_SCRIPT" << EOF
#!/usr/bin/env bash
set -euo pipefail

echo "🔧 Running $MILESTONE_ID Acceptance Tests..."

# Auto-generated acceptance test suite
# Customize based on milestone requirements

# Test 1: Validation script passes
echo "1️⃣ Testing validation script..."
if node "$OUTPUT_DIR/validate-${MILESTONE_ID}.mjs"; then
    echo "✅ Validation passed"
else
    echo "❌ Validation failed"
    exit 1
fi

# Add more tests based on milestone deliverables

echo "🎉 All $MILESTONE_ID acceptance tests passed!"
EOF

    chmod +x "$ACCEPTANCE_SCRIPT"
    log_success "Generated: $ACCEPTANCE_SCRIPT"
}

generate_execution_guide() {
    log_info "Generating execution guide..."

    GUIDE_FILE="$OUTPUT_DIR/agent-execution-guide-${MILESTONE_ID}.md"

    cat > "$GUIDE_FILE" << EOF
# $MILESTONE_ID Agent Execution Guide

## Overview
Auto-generated execution guide for $MILESTONE_TITLE

## Pre-Execution Validation
\`\`\`bash
# Run validation script
node docs/scripts/validate-${MILESTONE_ID}.mjs
# Expected: Exit code 0, confidence ≥${CONFIDENCE_THRESHOLD}%
\`\`\`

## Implementation Steps
<!-- Steps will be extracted from task breakdown -->

## Success Criteria Validation
<!-- Criteria will be extracted from milestone specification -->

## Final Acceptance Test
\`\`\`bash
bash docs/scripts/acceptance/${MILESTONE_ID}-acceptance.sh
\`\`\`

---
*Generated by auto-analyze-milestone.sh on $(date)*
EOF

    log_success "Generated: $GUIDE_FILE"
}

generate_analysis_report() {
    log_info "Generating analysis report..."

    REPORT_FILE="$OUTPUT_DIR/${MILESTONE_ID}-analysis-summary.md"

    cat > "$REPORT_FILE" << EOF
# $MILESTONE_ID Analysis Summary

## Executive Summary
**Confidence Score: $CONFIDENCE_SCORE%**
**Status: $([ $CONFIDENCE_SCORE -ge $CONFIDENCE_THRESHOLD ] && echo "READY FOR EXECUTION" || echo "NEEDS ATTENTION")**

## Validation Results
- Total Checks: $TOTAL_CHECKS
- Passed: $PASSED_CHECKS
- Errors: $ERRORS
- Warnings: $WARNINGS

## Generated Artifacts
- Validation Script: \`validate-${MILESTONE_ID}.mjs\`
- Acceptance Tests: \`acceptance/${MILESTONE_ID}-acceptance.sh\`
- Execution Guide: \`agent-execution-guide-${MILESTONE_ID}.md\`

## Recommendation
$([ $CONFIDENCE_SCORE -ge $CONFIDENCE_THRESHOLD ] && echo "✅ PROCEED WITH AGENT EXECUTION" || echo "⚠️ RESOLVE ISSUES BEFORE EXECUTION")

---
*Analysis completed on $(date)*
*Generated by auto-analyze-milestone.sh*
EOF

    log_success "Generated: $REPORT_FILE"
}

generate_core_analyzer() {
    log_info "Generating core analyzer..."

    cat > "$SCRIPT_DIR/analyze-milestone-core.mjs" << 'EOF'
#!/usr/bin/env node

/**
 * Core Milestone Analyzer
 * Performs comprehensive milestone analysis
 */

import { readFileSync, existsSync } from 'fs';

class MilestoneAnalyzer {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.errors = [];
    this.warnings = [];
  }

  analyze(milestoneFile) {
    if (!existsSync(milestoneFile)) {
      this.errors.push(`Milestone file not found: ${milestoneFile}`);
      return this.getResults();
    }

    const content = readFileSync(milestoneFile, 'utf8');

    this.validateFrontmatter(content);
    this.validateSections(content);
    this.validateTaskBreakdown(content);
    this.validateSuccessCriteria(content);

    return this.getResults();
  }

  validateFrontmatter(content) {
    const requiredFields = ['title', 'description', 'version', 'status'];
    const frontmatterMatch = content.match(/^---\s*\n([\s\S]*?)\n---/);

    if (!frontmatterMatch) {
      this.errors.push('Missing frontmatter');
      return;
    }

    requiredFields.forEach(field => {
      this.checks++;
      if (frontmatterMatch[1].includes(`${field}:`)) {
        this.passed++;
      } else {
        this.errors.push(`Missing frontmatter field: ${field}`);
      }
    });
  }

  validateSections(content) {
    const requiredSections = [
      'Definition of Done',
      'Deliverables',
      'Task Breakdown',
      'Success Criteria'
    ];

    requiredSections.forEach(section => {
      this.checks++;
      if (content.includes(section)) {
        this.passed++;
      } else {
        this.errors.push(`Missing section: ${section}`);
      }
    });
  }

  validateTaskBreakdown(content) {
    this.checks++;
    const taskMatches = content.match(/\| \d+ \|.*\|.*\|.*\|/g);
    if (taskMatches && taskMatches.length >= 3) {
      this.passed++;
    } else {
      this.errors.push('Insufficient task breakdown');
    }
  }

  validateSuccessCriteria(content) {
    this.checks++;
    const criteriaMatches = content.match(/- \[ \] \*\*SC-\d+\*\*/g);
    if (criteriaMatches && criteriaMatches.length >= 3) {
      this.passed++;
    } else {
      this.errors.push('Insufficient success criteria');
    }
  }

  getResults() {
    const confidence_score = Math.round((this.passed / this.checks) * 100);

    return {
      confidence_score,
      total_checks: this.checks,
      passed_checks: this.passed,
      errors: this.errors,
      warnings: this.warnings
    };
  }
}

// CLI interface
const milestoneFile = process.argv[2];
const outputJson = process.argv.includes('--json');

if (!milestoneFile) {
  console.error('Usage: node analyze-milestone-core.mjs <milestone-file> [--json]');
  process.exit(1);
}

const analyzer = new MilestoneAnalyzer();
const results = analyzer.analyze(milestoneFile);

if (outputJson) {
  console.log(JSON.stringify(results, null, 2));
} else {
  console.log(`Confidence: ${results.confidence_score}%`);
  console.log(`Checks: ${results.passed_checks}/${results.total_checks}`);
  if (results.errors.length > 0) {
    console.log('Errors:', results.errors);
  }
}

process.exit(results.confidence_score >= 95 ? 0 : 1);
EOF

    chmod +x "$SCRIPT_DIR/analyze-milestone-core.mjs"
}

main() {
    echo "🚀 Automated Milestone Analysis"
    echo "================================"

    parse_arguments "$@"
    extract_milestone_info
    run_core_analysis

    if [[ $SCRIPTS_ONLY == false ]]; then
        generate_analysis_report
        generate_execution_guide
    fi

    generate_validation_scripts
    generate_acceptance_tests

    echo ""
    echo "📊 Analysis Summary"
    echo "==================="
    log_info "Milestone: $MILESTONE_TITLE"
    log_info "Confidence Score: $CONFIDENCE_SCORE%"
    log_info "Checks Passed: $PASSED_CHECKS/$TOTAL_CHECKS"

    if [[ $CONFIDENCE_SCORE -ge $CONFIDENCE_THRESHOLD ]]; then
        log_success "✅ MILESTONE READY FOR AGENT EXECUTION"
        echo ""
        echo "📁 Generated Files:"
        echo "   • Validation: validate-${MILESTONE_ID}.mjs"
        echo "   • Acceptance: acceptance/${MILESTONE_ID}-acceptance.sh"
        if [[ $SCRIPTS_ONLY == false ]]; then
            echo "   • Guide: agent-execution-guide-${MILESTONE_ID}.md"
            echo "   • Report: ${MILESTONE_ID}-analysis-summary.md"
        fi
        exit 0
    else
        log_warn "⚠️ MILESTONE NEEDS ATTENTION (${CONFIDENCE_SCORE}% < ${CONFIDENCE_THRESHOLD}%)"
        exit 1
    fi
}

main "$@"
