#!/usr/bin/env node

/**
 * Core Milestone Analyzer
 * Performs comprehensive milestone analysis
 */

import { readFileSync, existsSync } from 'fs';

class MilestoneAnalyzer {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.errors = [];
    this.warnings = [];
  }

  analyze(milestoneFile) {
    if (!existsSync(milestoneFile)) {
      this.errors.push(`Milestone file not found: ${milestoneFile}`);
      return this.getResults();
    }

    const content = readFileSync(milestoneFile, 'utf8');

    this.validateFrontmatter(content);
    this.validateSections(content);
    this.validateTaskBreakdown(content);
    this.validateSuccessCriteria(content);

    return this.getResults();
  }

  validateFrontmatter(content) {
    const requiredFields = ['title', 'description', 'version', 'status'];
    const frontmatterMatch = content.match(/^---\s*\n([\s\S]*?)\n---/);

    if (!frontmatterMatch) {
      this.errors.push('Missing frontmatter');
      return;
    }

    requiredFields.forEach(field => {
      this.checks++;
      if (frontmatterMatch[1].includes(`${field}:`)) {
        this.passed++;
      } else {
        this.errors.push(`Missing frontmatter field: ${field}`);
      }
    });
  }

  validateSections(content) {
    const requiredSections = [
      'Definition of Done',
      'Deliverables',
      'Task Breakdown',
      'Success Criteria'
    ];

    requiredSections.forEach(section => {
      this.checks++;
      if (content.includes(section)) {
        this.passed++;
      } else {
        this.errors.push(`Missing section: ${section}`);
      }
    });
  }

  validateTaskBreakdown(content) {
    this.checks++;
    const taskMatches = content.match(/\| \d+ \|.*\|.*\|.*\|/g);
    if (taskMatches && taskMatches.length >= 3) {
      this.passed++;
    } else {
      this.errors.push('Insufficient task breakdown');
    }
  }

  validateSuccessCriteria(content) {
    this.checks++;
    const criteriaMatches = content.match(/- \[ \] \*\*SC-\d+\*\*/g);
    if (criteriaMatches && criteriaMatches.length >= 3) {
      this.passed++;
    } else {
      this.errors.push('Insufficient success criteria');
    }
  }

  getResults() {
    const confidence_score = Math.round((this.passed / this.checks) * 100);

    return {
      confidence_score,
      total_checks: this.checks,
      passed_checks: this.passed,
      errors: this.errors,
      warnings: this.warnings
    };
  }
}

// CLI interface
const milestoneFile = process.argv[2];
const outputJson = process.argv.includes('--json');

if (!milestoneFile) {
  console.error('Usage: node analyze-milestone-core.mjs <milestone-file> [--json]');
  process.exit(1);
}

const analyzer = new MilestoneAnalyzer();
const results = analyzer.analyze(milestoneFile);

if (outputJson) {
  console.log(JSON.stringify(results, null, 2));
} else {
  console.log(`Confidence: ${results.confidence_score}%`);
  console.log(`Checks: ${results.passed_checks}/${results.total_checks}`);
  if (results.errors.length > 0) {
    console.log('Errors:', results.errors);
  }
}

process.exit(results.confidence_score >= 95 ? 0 : 1);
