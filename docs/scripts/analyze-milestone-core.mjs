#!/usr/bin/env node

/**
 * Core Milestone Analyzer
 * Performs comprehensive milestone analysis
 */

import { readFileSync, existsSync } from "fs";

class MilestoneAnalyzer {
  constructor() {
    this.checks = 0;
    this.passed = 0;
    this.errors = [];
    this.warnings = [];
  }

  analyze(milestoneFile) {
    if (!existsSync(milestoneFile)) {
      this.errors.push(`Milestone file not found: ${milestoneFile}`);
      return this.getResults();
    }

    const content = readFileSync(milestoneFile, "utf8");

    this.validateFrontmatter(content);
    this.validateSections(content);
    this.validateTaskBreakdown(content);
    this.validateSuccessCriteria(content);
    this.validateToolchainVersions(content);
    this.validateDeliverableAlignment(content);
    this.validateBranchNamingConsistency(content);
    this.validateDomainSpecificRequirements(content);

    return this.getResults();
  }

  validateFrontmatter(content) {
    const requiredFields = ["title", "description", "version", "status"];
    const frontmatterMatch = content.match(/^---\s*\n([\s\S]*?)\n---/);

    if (!frontmatterMatch) {
      this.errors.push("Missing frontmatter");
      return;
    }

    requiredFields.forEach((field) => {
      this.checks++;
      if (frontmatterMatch[1].includes(`${field}:`)) {
        this.passed++;
      } else {
        this.errors.push(`Missing frontmatter field: ${field}`);
      }
    });
  }

  validateSections(content) {
    const requiredSections = [
      "Definition of Done",
      "Deliverables",
      "Task Breakdown",
      "Success Criteria"
    ];

    requiredSections.forEach((section) => {
      this.checks++;
      if (content.includes(section)) {
        this.passed++;
      } else {
        this.errors.push(`Missing section: ${section}`);
      }
    });
  }

  validateTaskBreakdown(content) {
    this.checks++;
    const taskMatches = content.match(/\| \d+ \|.*\|.*\|.*\|/g);
    if (taskMatches && taskMatches.length >= 3) {
      this.passed++;
    } else {
      this.errors.push("Insufficient task breakdown");
    }
  }

  validateSuccessCriteria(content) {
    this.checks++;
    const criteriaMatches = content.match(/- \[ \] \*\*SC-\d+\*\*/g);
    if (criteriaMatches && criteriaMatches.length >= 3) {
      this.passed++;
    } else {
      this.errors.push("Insufficient success criteria");
    }
  }

  validateToolchainVersions(content) {
    const toolchainMatch = content.match(
      /## 🧳 Toolchain Versions\s*\n([\s\S]*?)(?=\n##|$)/
    );
    if (!toolchainMatch) {
      this.errors.push("Missing toolchain versions section");
      return;
    }

    const toolchainText = toolchainMatch[1];

    // Check for realistic Node.js versions
    this.checks++;
    const nodeMatch = toolchainText.match(/node:\s*["']?(\d+\.\d+\.\d+)["']?/);
    if (nodeMatch) {
      const nodeVersion = nodeMatch[1];
      const majorVersion = parseInt(nodeVersion.split(".")[0]);
      if (majorVersion >= 18 && majorVersion <= 22) {
        this.passed++;
      } else {
        this.warnings.push(
          `Node.js version ${nodeVersion} may be outdated or too new`
        );
      }
    } else {
      this.errors.push("Node.js version not specified in toolchain");
    }

    // Check for package manager version
    this.checks++;
    if (
      toolchainText.includes("pnpm:") ||
      toolchainText.includes("npm:") ||
      toolchainText.includes("yarn:")
    ) {
      this.passed++;
    } else {
      this.errors.push("Package manager not specified in toolchain");
    }

    // Check for TypeScript version if mentioned
    this.checks++;
    const tsMatch = toolchainText.match(
      /typescript:\s*["']?(\d+\.\d+\.\d+)["']?/
    );
    if (tsMatch) {
      const tsVersion = tsMatch[1];
      const majorVersion = parseInt(tsVersion.split(".")[0]);
      if (majorVersion >= 4 && majorVersion <= 6) {
        this.passed++;
      } else {
        this.warnings.push(
          `TypeScript version ${tsVersion} may be incompatible`
        );
      }
    } else {
      this.passed++; // TypeScript is optional
    }
  }

  validateDeliverableAlignment(content) {
    const deliverablesMatch = content.match(
      /## 📦 Deliverables\s*\n([\s\S]*?)(?=\n##|$)/
    );
    const taskBreakdownMatch = content.match(
      /## 🔨 Task Breakdown\s*\n([\s\S]*?)(?=\n##|$)/
    );

    if (!deliverablesMatch || !taskBreakdownMatch) {
      this.errors.push(
        "Cannot validate deliverable alignment - missing sections"
      );
      return;
    }

    const deliverables = deliverablesMatch[1];
    const taskBreakdown = taskBreakdownMatch[1];

    // Extract key deliverable paths
    const deliverablePaths = [];
    const pathMatches = deliverables.match(
      /`([^`]+\.(ts|js|yml|yaml|json|md))`/g
    );
    if (pathMatches) {
      pathMatches.forEach((match) => {
        const path = match.replace(/`/g, "");
        deliverablePaths.push(path);
      });
    }

    // Check if major deliverables are mentioned in tasks
    this.checks++;
    let alignmentScore = 0;
    const totalDeliverables = deliverablePaths.length;

    deliverablePaths.forEach((path) => {
      const fileName = path.split("/").pop();
      const baseName = fileName.split(".")[0];

      if (
        taskBreakdown.toLowerCase().includes(fileName.toLowerCase()) ||
        taskBreakdown.toLowerCase().includes(baseName.toLowerCase())
      ) {
        alignmentScore++;
      }
    });

    if (totalDeliverables > 0 && alignmentScore / totalDeliverables >= 0.7) {
      this.passed++;
    } else {
      this.warnings.push(
        `Poor deliverable-task alignment: ${alignmentScore}/${totalDeliverables} deliverables mentioned in tasks`
      );
    }
  }

  validateBranchNamingConsistency(content) {
    const taskBreakdownMatch = content.match(
      /## 🔨 Task Breakdown\s*\n([\s\S]*?)(?=\n##|$)/
    );
    if (!taskBreakdownMatch) return;

    const taskSection = taskBreakdownMatch[1];
    const tableRows = taskSection
      .split("\n")
      .filter((line) => line.trim().startsWith("|") && !line.includes("---"))
      .slice(1); // Skip header

    const branchNames = [];
    tableRows.forEach((row) => {
      const columns = row
        .split("|")
        .map((col) => col.trim())
        .filter((col) => col);
      if (columns.length >= 2) {
        const branchName = columns[1].replace(/`/g, "");
        if (branchName && branchName !== "Branch name") {
          branchNames.push(branchName);
        }
      }
    });

    this.checks++;
    if (branchNames.length === 0) {
      this.errors.push("No branch names found in task breakdown");
      return;
    }

    // Check for consistent naming pattern
    const milestonePrefix = this.extractMilestoneId(content);
    const expectedPattern = new RegExp(`^${milestonePrefix}/[a-z-]+$`);

    let consistentBranches = 0;
    branchNames.forEach((branch) => {
      if (expectedPattern.test(branch)) {
        consistentBranches++;
      }
    });

    if (consistentBranches / branchNames.length >= 0.8) {
      this.passed++;
    } else {
      this.warnings.push(
        `Inconsistent branch naming: ${consistentBranches}/${branchNames.length} branches follow pattern`
      );
    }

    // Check for duplicate branch names
    this.checks++;
    const uniqueBranches = new Set(branchNames);
    if (branchNames.length === uniqueBranches.size) {
      this.passed++;
    } else {
      this.errors.push("Duplicate branch names found in task breakdown");
    }
  }

  validateDomainSpecificRequirements(content) {
    const title = content.match(/title:\s*(.+)/)?.[1] || "";

    // Knowledge Graph specific validations
    if (
      title.toLowerCase().includes("knowledge") ||
      title.toLowerCase().includes("graph")
    ) {
      this.validateKnowledgeGraphRequirements(content);
    }

    // API specific validations
    if (
      title.toLowerCase().includes("api") ||
      content.includes("REST") ||
      content.includes("GraphQL")
    ) {
      this.validateAPIRequirements(content);
    }

    // CI/CD specific validations
    if (
      title.toLowerCase().includes("ci") ||
      content.includes("github/workflows") ||
      content.includes("pipeline")
    ) {
      this.validateCICDRequirements(content);
    }
  }

  validateKnowledgeGraphRequirements(content) {
    // Check for schema definition
    this.checks++;
    if (
      content.includes("schema") &&
      (content.includes(".yml") ||
        content.includes(".yaml") ||
        content.includes("JSON-LD"))
    ) {
      this.passed++;
    } else {
      this.warnings.push(
        "Knowledge graph milestone should define schema format"
      );
    }

    // Check for parsing strategy
    this.checks++;
    if (
      content.includes("parse") ||
      content.includes("extract") ||
      content.includes("gray-matter")
    ) {
      this.passed++;
    } else {
      this.warnings.push(
        "Knowledge graph milestone should specify parsing strategy"
      );
    }

    // Check for output formats
    this.checks++;
    if (
      content.includes("JSON-LD") ||
      content.includes("YAML") ||
      content.includes("RDF")
    ) {
      this.passed++;
    } else {
      this.warnings.push(
        "Knowledge graph milestone should specify output formats"
      );
    }
  }

  validateAPIRequirements(content) {
    // Check for API specification
    this.checks++;
    if (
      content.includes("OpenAPI") ||
      content.includes("swagger") ||
      content.includes("schema")
    ) {
      this.passed++;
    } else {
      this.warnings.push("API milestone should include specification format");
    }

    // Check for testing strategy
    this.checks++;
    if (
      content.includes("test") &&
      (content.includes("endpoint") || content.includes("integration"))
    ) {
      this.passed++;
    } else {
      this.warnings.push("API milestone should include testing strategy");
    }
  }

  validateCICDRequirements(content) {
    // Check for workflow definition
    this.checks++;
    if (content.includes(".github/workflows") || content.includes("pipeline")) {
      this.passed++;
    } else {
      this.warnings.push("CI/CD milestone should define workflow files");
    }

    // Check for deployment strategy
    this.checks++;
    if (
      content.includes("deploy") ||
      content.includes("artifact") ||
      content.includes("release")
    ) {
      this.passed++;
    } else {
      this.warnings.push("CI/CD milestone should include deployment strategy");
    }
  }

  extractMilestoneId(content) {
    const titleMatch = content.match(
      /title:\s*.*[Mm]ilestone\s+([A-Za-z0-9.]+)/
    );
    if (titleMatch) {
      return titleMatch[1].toLowerCase();
    }
    return "m\\d+\\.\\d+"; // fallback pattern
  }

  getResults() {
    const confidence_score = Math.round((this.passed / this.checks) * 100);

    return {
      confidence_score,
      total_checks: this.checks,
      passed_checks: this.passed,
      errors: this.errors,
      warnings: this.warnings
    };
  }
}

// CLI interface
const milestoneFile = process.argv[2];
const outputJson = process.argv.includes("--json");

if (!milestoneFile) {
  console.error(
    "Usage: node analyze-milestone-core.mjs <milestone-file> [--json]"
  );
  process.exit(1);
}

const analyzer = new MilestoneAnalyzer();
const results = analyzer.analyze(milestoneFile);

if (outputJson) {
  console.log(JSON.stringify(results, null, 2));
} else {
  console.log(`Confidence: ${results.confidence_score}%`);
  console.log(`Checks: ${results.passed_checks}/${results.total_checks}`);
  if (results.errors.length > 0) {
    console.log("Errors:", results.errors);
  }
}

process.exit(results.confidence_score >= 95 ? 0 : 1);
