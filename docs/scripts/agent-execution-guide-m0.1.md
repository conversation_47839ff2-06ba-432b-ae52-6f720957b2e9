# M0.1 Agent Execution Guide

## Overview
This guide provides step-by-step instructions for software agents to execute Milestone M0.1 with 95% confidence.

## Pre-Execution Validation

### 1. Run Readiness Check
```bash
node docs/scripts/validate-m0.1-readiness.mjs
```
**Expected Result:** Success rate ≥95%

### 2. Verify Spec Lint
```bash
node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
```
**Expected Result:** Exit code 0

## Implementation Steps

### Phase 1: Package Setup

#### 1.1 Create spec-parser-lib package
```bash
cd code
mkdir -p packages/spec-parser-lib/src
mkdir -p packages/spec-parser-lib/tests
```

#### 1.2 Initialize spec-parser-lib package.json
```json
{
  "name": "@workflow-mapper/spec-parser-lib",
  "version": "0.1.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsup src/index.ts --format cjs,esm --dts",
    "test": "jest",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "gray-matter": "^4.0.3",
    "yaml": "^2.3.2",
    "uuid": "^9.0.0"
  },
  "devDependencies": {
    "@types/uuid": "^9.0.0",
    "jest": "^29.7.0",
    "@types/jest": "^29.5.12"
  }
}
```

#### 1.3 Create kg-cli package
```bash
mkdir -p packages/kg-cli/src
mkdir -p packages/kg-cli/tests
```

#### 1.4 Initialize kg-cli package.json
```json
{
  "name": "@workflow-mapper/kg-cli",
  "version": "0.1.0",
  "bin": {
    "build-kg": "dist/build-kg.js"
  },
  "scripts": {
    "build": "tsup src/build-kg.ts --format cjs --dts",
    "test": "jest",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "@workflow-mapper/spec-parser-lib": "workspace:*",
    "commander": "^11.0.0",
    "yaml": "^2.3.2"
  },
  "devDependencies": {
    "@types/node": "^20.12.7",
    "jest": "^29.7.0"
  }
}
```

### Phase 2: Core Implementation

#### 2.1 Implement parse-specs.ts
**File:** `code/packages/spec-parser-lib/src/parse-specs.ts`

Key functions to implement:
- `parseSpecFile(filePath: string): SpecNode`
- `extractFrontmatter(content: string): FrontmatterData`
- `extractSections(content: string): Section[]`
- `generateNodeId(spec: SpecNode): string`

#### 2.2 Implement build-kg.ts
**File:** `code/packages/kg-cli/src/build-kg.ts`

Key functions to implement:
- `main()` - CLI entry point
- `buildKnowledgeGraph(specsDir: string, dryRun: boolean)`
- `writeGraphFiles(graph: KnowledgeGraph, dryRun: boolean)`
- `scanSpecFiles(directory: string): string[]`

#### 2.3 Create kg-schema.yml
**File:** `kg-schema.yml` (repo root)

Define entities and relationships:
- milestone
- component  
- implements
- depends_on
- contains

### Phase 3: Integration

#### 3.1 Add build-kg script to main package.json
```json
{
  "scripts": {
    "build-kg": "pnpm --filter @workflow-mapper/kg-cli build-kg"
  }
}
```

#### 3.2 Create GitHub workflow
**File:** `.github/workflows/graph.yml`

```yaml
name: Graph Build
on:
  push:
    branches: [main]
  pull_request:

jobs:
  build-graph:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with: { version: 8.15.4 }
      - run: corepack enable && pnpm install
      - run: pnpm run build-kg -- --dry-run docs/tech-specs
```

#### 3.3 Create VS Code extensions.json
**File:** `.vscode/extensions.json`

```json
{
  "recommendations": [
    "unifiedjs.vscode-mdx",
    "shd101wyy.markdown-preview-enhanced",
    "yzhang.markdown-all-in-one"
  ]
}
```

#### 3.4 Create docs README
**File:** `docs/README.md`

Include:
- How to preview MDX in VS Code
- How to use with Obsidian
- Knowledge graph usage

### Phase 4: Testing

#### 4.1 Write unit tests
- Test spec parsing functionality
- Test CLI dry-run mode
- Test graph generation

#### 4.2 Run acceptance tests
```bash
bash docs/scripts/acceptance/m0.1-acceptance.sh
```

## Validation Commands

### Success Criteria Validation

#### SC-1: Dry-run test
```bash
pnpm run build-kg -- --dry-run docs/tech-specs
# Expected: Exit code 0, no files written
```

#### SC-2: Full build test
```bash
pnpm run build-kg
ls kg.jsonld kg.yaml  # Both files should exist
```

#### SC-3: CI test
```bash
# Push to branch and verify GitHub Actions pass
```

#### SC-4: Graph content validation
```bash
yq '.entities.milestone' kg.yaml  # Should show milestone nodes
yq '.entities.component' kg.yaml  # Should show component nodes
yq '.relationships[] | select(.type == "implements")' kg.yaml  # Should show implements edges
```

#### SC-5: Spec lint validation
```bash
node scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx
# Expected: Exit code 0
```

#### SC-6: Agent dry-run validation
```bash
pnpm run agent:dry-run --spec docs/tech-specs/milestones/milestone-M0.1.mdx
# Expected: Exit code 0
```

## Common Issues and Solutions

### Issue: pnpm workspace not recognizing packages
**Solution:** Ensure `pnpm-workspace.yaml` includes `packages/*`

### Issue: TypeScript compilation errors
**Solution:** Check tsconfig.json includes proper paths and references

### Issue: Gray-matter parsing errors
**Solution:** Verify MDX frontmatter format is valid YAML

### Issue: CLI command not found
**Solution:** Run `pnpm install` and ensure bin field in package.json is correct

## Final Validation

Before marking milestone complete:

1. ✅ All success criteria pass
2. ✅ Acceptance tests pass
3. ✅ CI pipeline is green
4. ✅ Graph files are generated correctly
5. ✅ Spec lint passes
6. ✅ Documentation is complete

## Agent Confidence Indicators

- **95%+ confidence:** All validation scripts pass, clear error messages, deterministic behavior
- **90-94% confidence:** Minor warnings but core functionality works
- **<90% confidence:** Significant issues require human intervention

## Next Steps

After M0.1 completion:
1. Tag release: `kg-bootstrap-v0.1.0`
2. Update milestone status to "Completed"
3. Begin M0.2 planning
