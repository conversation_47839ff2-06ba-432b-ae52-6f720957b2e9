#!/usr/bin/env bash
set -euo pipefail

echo "🔗 Running M0.1 Knowledge-Graph Bootstrap Acceptance Tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
pass_test() {
    echo -e "${GREEN}✅ $1${NC}"
    ((TESTS_PASSED++))
}

fail_test() {
    echo -e "${RED}❌ $1${NC}"
    ((TESTS_FAILED++))
}

warn_test() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up test artifacts..."
    rm -f kg.jsonld kg.yaml 2>/dev/null || true
}

# Set trap for cleanup
trap cleanup EXIT

echo "📋 M0.1 Acceptance Test Suite"
echo "=============================="

# Test 1: Spec Lint Validation
echo -e "\n1️⃣ Testing spec lint validation..."
if node docs/scripts/spec-lint.mjs docs/tech-specs/milestones/milestone-M0.1.mdx; then
    pass_test "Spec lint validation passed"
else
    fail_test "Spec lint validation failed"
fi

# Test 2: Required Dependencies Check
echo -e "\n2️⃣ Testing required dependencies..."
cd code

# Check if required packages are available
REQUIRED_DEPS=("gray-matter" "yaml" "uuid" "typescript")
for dep in "${REQUIRED_DEPS[@]}"; do
    if pnpm list "$dep" >/dev/null 2>&1; then
        pass_test "Dependency $dep is available"
    else
        fail_test "Dependency $dep is missing"
    fi
done

# Test 3: Package Structure Validation
echo -e "\n3️⃣ Testing package structure..."

# Check if workspace includes packages/*
if grep -q "packages/\*" pnpm-workspace.yaml; then
    pass_test "pnpm-workspace.yaml includes packages/*"
else
    fail_test "pnpm-workspace.yaml missing packages/* glob"
fi

# Test 4: Required Directories Check
echo -e "\n4️⃣ Testing required directory structure..."
REQUIRED_DIRS=(
    "packages"
    "apps"
    "apps/api"
    "apps/web"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        pass_test "Directory $dir exists"
    else
        fail_test "Directory $dir missing"
    fi
done

# Test 5: Check for spec-parser-lib package structure
echo -e "\n5️⃣ Testing spec-parser-lib package readiness..."
if [ -d "packages/spec-parser-lib" ]; then
    pass_test "spec-parser-lib package directory exists"
    
    if [ -f "packages/spec-parser-lib/package.json" ]; then
        pass_test "spec-parser-lib package.json exists"
    else
        warn_test "spec-parser-lib package.json missing (will be created)"
    fi
    
    if [ -f "packages/spec-parser-lib/src/parse-specs.ts" ]; then
        pass_test "parse-specs.ts exists"
    else
        warn_test "parse-specs.ts missing (will be created)"
    fi
else
    warn_test "spec-parser-lib package missing (will be created)"
fi

# Test 6: Check for kg-cli package structure
echo -e "\n6️⃣ Testing kg-cli package readiness..."
if [ -d "packages/kg-cli" ]; then
    pass_test "kg-cli package directory exists"
    
    if [ -f "packages/kg-cli/package.json" ]; then
        pass_test "kg-cli package.json exists"
    else
        warn_test "kg-cli package.json missing (will be created)"
    fi
    
    if [ -f "packages/kg-cli/src/build-kg.ts" ]; then
        pass_test "build-kg.ts exists"
    else
        warn_test "build-kg.ts missing (will be created)"
    fi
else
    warn_test "kg-cli package missing (will be created)"
fi

# Test 7: Check for build-kg script availability
echo -e "\n7️⃣ Testing build-kg script availability..."
if grep -q "build-kg" package.json; then
    pass_test "build-kg script defined in package.json"
else
    warn_test "build-kg script missing from package.json (will be added)"
fi

# Test 8: Dry-run capability test (if build-kg exists)
echo -e "\n8️⃣ Testing dry-run capability..."
cd ..
if command -v pnpm >/dev/null 2>&1 && pnpm run build-kg --help 2>/dev/null | grep -q "dry-run"; then
    pass_test "build-kg supports --dry-run flag"
    
    # Test actual dry-run
    if pnpm run build-kg -- --dry-run docs/tech-specs >/dev/null 2>&1; then
        pass_test "Dry-run execution successful"
        
        # Verify no files were created
        if [ ! -f "kg.jsonld" ] && [ ! -f "kg.yaml" ]; then
            pass_test "Dry-run correctly avoided file creation"
        else
            fail_test "Dry-run incorrectly created files"
        fi
    else
        warn_test "Dry-run execution failed (implementation needed)"
    fi
else
    warn_test "build-kg command not available yet (will be implemented)"
fi

# Test 9: CI Configuration Check
echo -e "\n9️⃣ Testing CI configuration..."
if [ -f ".github/workflows/graph.yml" ]; then
    pass_test "GitHub workflow graph.yml exists"
    
    if grep -q "build-kg.*--dry-run" .github/workflows/graph.yml; then
        pass_test "CI workflow includes dry-run test"
    else
        fail_test "CI workflow missing dry-run test"
    fi
else
    warn_test "GitHub workflow graph.yml missing (will be created)"
fi

# Test 10: Editor Support Check
echo -e "\n🔟 Testing editor support..."
if [ -f ".vscode/extensions.json" ]; then
    pass_test ".vscode/extensions.json exists"
else
    warn_test ".vscode/extensions.json missing (will be created)"
fi

if [ -f "docs/README.md" ]; then
    pass_test "docs/README.md exists"
else
    warn_test "docs/README.md missing (will be created)"
fi

# Test 11: Schema File Check
echo -e "\n1️⃣1️⃣ Testing schema file..."
if [ -f "kg-schema.yml" ]; then
    pass_test "kg-schema.yml exists"
else
    warn_test "kg-schema.yml missing (will be created)"
fi

# Test 12: MDX Content Validation
echo -e "\n1️⃣2️⃣ Testing MDX content structure..."
if find docs/tech-specs -name "*.mdx" -type f | head -1 | xargs grep -q "^---$"; then
    pass_test "MDX files have frontmatter"
else
    fail_test "MDX files missing frontmatter"
fi

# Summary
echo -e "\n📊 Test Summary"
echo "==============="
echo -e "Tests passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests failed: ${RED}$TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}All critical tests passed!${NC}"
    echo "✅ M0.1 milestone is ready for implementation"
    exit 0
else
    echo -e "\n⚠️  ${YELLOW}Some tests failed, but this may be expected for pre-implementation validation${NC}"
    echo "🔧 Review failed tests and implement missing components"
    exit 1
fi
