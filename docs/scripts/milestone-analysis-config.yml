# Milestone Analysis Configuration
# Customize analysis behavior for different milestone types

# Analysis strictness level
strictness_level: "high"  # high, medium, low

# Confidence scoring
confidence:
  threshold: 95
  weights:
    frontmatter: 20
    sections: 30
    task_breakdown: 25
    success_criteria: 15
    deliverables: 10

# Required frontmatter fields
required_frontmatter:
  - title
  - description
  - created
  - version
  - status
  - tags

# Optional frontmatter fields (warnings only)
optional_frontmatter:
  - updated
  - authors

# Required sections (exact matches)
required_sections:
  - "## 🧳 Toolchain Versions"
  - "## 🎯 Definition of Done"
  - "## 📦 Deliverables"
  - "## 🗂 Directory Layout"
  - "## 🧠 Key Decisions"
  - "## ✅ Success Criteria"
  - "## 🔨 Task Breakdown"
  - "## 🤖 CI Pipeline"
  - "## 🧪 Acceptance Tests"

# Minimum requirements
minimums:
  tasks: 5
  success_criteria: 3
  deliverables: 3

# Task validation
task_validation:
  require_branch_names: true
  require_owners: true
  branch_pattern: "^m\\d+\\.\\d+/[a-z-]+$"
  valid_owners: ["BE", "FE", "PM", "DevOps", "Lead"]
  action_verbs: ["scaffold", "implement", "create", "add", "commit", "run", "merge", "build", "test", "deploy"]

# Success criteria validation
success_criteria:
  pattern: "- \\[ \\] \\*\\*SC-\\d+\\*\\*"
  require_commands: true
  require_expected_results: true

# Deliverables validation
deliverables:
  require_paths: true
  require_descriptions: true
  check_coverage: true

# Output configuration
output:
  generate_validation_scripts: true
  generate_acceptance_tests: true
  generate_execution_guides: true
  generate_analysis_reports: true
  include_templates: true
  verbose_logging: false

# Template customization
templates:
  validation_script: "default"
  acceptance_test: "default"
  execution_guide: "default"
  analysis_report: "detailed"

# Agent-specific settings
agent_settings:
  execution_confidence_threshold: 95
  warning_tolerance: 5
  error_tolerance: 0
  require_deterministic_tests: true
  require_rollback_plan: false

# Integration settings
integration:
  ci_cd: true
  github_actions: true
  quality_gates: true
  automated_reporting: true
