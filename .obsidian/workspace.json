{"main": {"id": "172db7ad275e2055", "type": "split", "children": [{"id": "770efb8b977b1396", "type": "tabs", "children": [{"id": "439cddcece3e5f13", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}]}], "direction": "vertical"}, "left": {"id": "3ce5a687488dab9b", "type": "split", "children": [{"id": "3d5cbf29f0ce5220", "type": "tabs", "children": [{"id": "1112416cc676ea02", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "015a7480fddbc8e0", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "4f4e6158eee6c96c", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "d56ad8ceac812aa0", "type": "split", "children": [{"id": "752c0f00c5e7c48d", "type": "tabs", "children": [{"id": "f58c166d3770ac57", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks"}}, {"id": "53f567dafce85b9f", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links"}}, {"id": "a16ede2641b5d1e2", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "3382b3f8679fa627", "type": "leaf", "state": {"type": "outline", "state": {"followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "1112416cc676ea02", "lastOpenFiles": ["docs/scripts/AUTOMATED-MILESTONE-ANALYSIS-SYSTEM.md", "docs/scripts/demo-automated-analysis.sh", "docs/scripts/automated-analysis-guide.md", "docs/scripts/milestone-analysis-config.yml", "docs/scripts/validate-M0.1.mjs", "docs/scripts/analyze-milestone-core.mjs", "docs/scripts/auto-analyze-milestone.sh", "docs/tech-specs/adrs/adr-005-automated-milestone-analysis.md", "docs/scripts/agent-execution-checklist.md", "docs/scripts/validate-m0.1-final.mjs", "docs/scripts/m0.1-analysis-summary.md", "docs/scripts/validate-task-breakdown.mjs", "docs/scripts/agent-execution-guide-m0.1.md", "docs/scripts/validate-m0.1-readiness.mjs", "docs/scripts/acceptance/m0.1-acceptance.sh", "docs/tech-specs/milestones/milestone-M0.1.mdx", "work-log/milestone-test/requirement-checklist.md", "work-log/milestone-test/implementation-log.md", "milestone-experiment-1.md", "docs/tech-specs/archived/milestones/milestone-m0.1/work-log/fixes-checklist.md", "docs/tech-specs/archived/milestones/milestone-m0.1/work-log/mdx-build-analysis.md", "docs/tech-specs/archived/milestones/milestone-m0.1/work-log/technical-reference.md", "docs/tech-specs/archived/milestones/milestone-m0.1/work-log/mdx-build-rca.md", "docs/tech-specs/archived/milestones/milestone-m0.1/work-log/requirement-checklist.md", "docs/tech-specs/archived/milestones/milestone-m0.1/work-log/mdx-build-investigation.md", "docs/tech-specs/archived/milestones/milestone-m0.1/work-log/implementation-log.md", "docs/tech-specs/archived/milestones/milestone-m0.1/README.md", "code/apps/docs-site/test-docs/intro.md", "code/apps/docs-site/static/img/docusaurus.png", "code/apps/docs-site/static/img/undraw_docusaurus_tree.svg", "code/apps/docs-site/static/img/undraw_docusaurus_react.svg", "code/apps/docs-site/static/img/undraw_docusaurus_mountain.svg", "code/apps/docs-site/static/img/logo.svg", "code/apps/docs-site/static/img/docusaurus-social-card.jpg", "code/apps/docs-site/src/pages/markdown-page.md", "code/apps/docs-site/docs/tutorial-extras/img/localeDropdown.png", "code/apps/docs-site/docs/tutorial-extras/img/docsVersionDropdown.png", "code/apps/docs-site/docs/tutorial-basics/deploy-your-site.md", "code/apps/docs-site/docs/tutorial-basics/create-a-page.md", "code/apps/docs-site/docs/tutorial-basics/create-a-document.md", "code/apps/docs-site/docs/tutorial-basics/create-a-blog-post.md", "code/apps/docs-site/docs/tutorial-basics/congratulations.md", "code/apps/docs-site/docs/tutorial-extras/translate-your-site.md", "code/apps/docs-site/docs/tutorial-extras/manage-docs-versions.md", "code/apps/docs-site/blog/2021-08-26-welcome/docusaurus-plushie-banner.jpeg"]}